# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project
# Copyright 2024 Jules, an AI-powered software engineer.
"""
Attention layer with a custom Triton kernel for Turing GPUs (Compute Capability 7.5).

This backend is designed to provide a memory-efficient attention mechanism for
NVIDIA's Turing architecture, which does not support modern FlashAttention-2.

NOTE: This implementation accelerates the **prefill phase** of attention by using
a custom Triton kernel. The **decode phase** reuses the existing, highly-optimized
PagedAttention CUDA kernel from vLLM, as this is typically faster for single-
token decoding than a generic Triton implementation.
"""
import os
from dataclasses import dataclass, field
from typing import List, Optional, Tuple, Type

import torch

from vllm.attention.backends.abstract import (AttentionBackend, AttentionImpl,
                                              AttentionLayer, AttentionMetadata,
                                              AttentionType)
from vllm.attention.backends.utils import (CommonAttentionState,
                                           CommonMetadataBuilder)
from vllm.attention.ops.paged_attn import (PagedAttention,
                                           PagedAttentionMetadata)
from vllm.logger import init_logger

# Import Triton conditionally to handle environments where it's not available
try:
    import triton
    import triton.language as tl
    TRITON_AVAILABLE = True
    # We need to set this environment variable to allow Triton to use the legacy PTX assembler,
    # which is necessary for older compute capabilities like 7.5 (Turing).
    os.environ['TRITON_USE_LEGACY_PTX_ASSEMBLER'] = '1'
except ImportError:
    TRITON_AVAILABLE = False
    logger = init_logger(__name__)
    logger.warning("Triton not available, Turing attention backend will use fallback implementations")

logger = init_logger(__name__)


class TuringAttentionBackend(AttentionBackend):
    accept_output_buffer: bool = True

    @staticmethod
    def get_name() -> str:
        return "Turing"

    @staticmethod
    def get_impl_cls() -> Type["TuringAttentionImpl"]:
        return TuringAttentionImpl

    @staticmethod
    def get_metadata_cls() -> Type["AttentionMetadata"]:
        return TuringAttentionMetadata

    @staticmethod
    def get_builder_cls() -> Type["TuringAttentionMetadataBuilder"]:
        return TuringAttentionMetadataBuilder

    @staticmethod
    def get_state_cls() -> Type["CommonAttentionState"]:
        return CommonAttentionState

    @staticmethod
    def get_supported_head_sizes() -> List[int]:
        return [16, 32, 64, 128]

    @staticmethod
    def get_supported_dtypes() -> List[torch.dtype]:
        return [torch.float16]

    @staticmethod
    def get_kv_cache_shape(
        num_blocks: int,
        block_size: int,
        num_kv_heads: int,
        head_size: int,
    ) -> Tuple[int, ...]:
        return PagedAttention.get_kv_cache_shape(num_blocks, block_size,
                                                 num_kv_heads, head_size)

    @staticmethod
    def get_kv_cache_stride_order() -> Tuple[int, ...]:
        # Use the same stride order as PagedAttention for compatibility
        return (0, 1, 2)

    @staticmethod
    def swap_blocks(
        src_kv_cache: torch.Tensor,
        dst_kv_cache: torch.Tensor,
        src_to_dst: torch.Tensor,
    ) -> None:
        PagedAttention.swap_blocks(src_kv_cache, dst_kv_cache, src_to_dst)

    @staticmethod
    def copy_blocks(
        kv_caches: List[torch.Tensor],
        src_to_dists: torch.Tensor,
    ) -> None:
        PagedAttention.copy_blocks(kv_caches, src_to_dists)


@dataclass
class TuringAttentionMetadata(AttentionMetadata, PagedAttentionMetadata):
    """
    Metadata for the TuringAttentionBackend.
    This is largely based on XFormersMetadata, as the metadata needed is
    determined by the PagedAttention mechanism rather than the core kernel.
    """
    # Additional fields specific to Turing backend
    seq_lens: Optional[List[int]]
    max_query_len: int
    max_prefill_seq_len: int
    use_cuda_graph: bool
    seq_start_loc: Optional[torch.Tensor]
    context_lens_tensor: Optional[torch.Tensor]
    query_start_loc: Optional[torch.Tensor]
    max_decode_query_len: Optional[int] = None

    _cached_prefill_metadata: Optional["TuringAttentionMetadata"] = field(default=None)
    _cached_decode_metadata: Optional["TuringAttentionMetadata"] = field(default=None)

    @property
    def prefill_metadata(self) -> Optional["TuringAttentionMetadata"]:
        if self.num_prefills == 0:
            return None

        if self._cached_prefill_metadata is not None:
            return self._cached_prefill_metadata

        self._cached_prefill_metadata = TuringAttentionMetadata(
            num_prefills=self.num_prefills,
            num_prefill_tokens=self.num_prefill_tokens,
            num_decode_tokens=0,
            slot_mapping=self.slot_mapping[:self.num_prefill_tokens],
            multi_modal_placeholder_index_maps=self.multi_modal_placeholder_index_maps,
            enable_kv_scales_calculation=self.enable_kv_scales_calculation,
            seq_lens_tensor=self.seq_lens_tensor[:self.num_prefills] if self.seq_lens_tensor is not None else None,
            max_decode_seq_len=0,
            block_tables=self.block_tables[:self.num_prefills] if self.block_tables is not None else None,
            seq_lens=self.seq_lens[:self.num_prefills] if self.seq_lens else None,
            max_query_len=self.max_query_len,
            max_decode_query_len=getattr(self, 'max_decode_query_len', None),
            max_prefill_seq_len=self.max_prefill_seq_len,
            use_cuda_graph=False,
            seq_start_loc=None,
            context_lens_tensor=self.context_lens_tensor[:self.num_prefills] if self.context_lens_tensor is not None else None,
            query_start_loc=self.query_start_loc[:self.num_prefills + 1] if self.query_start_loc is not None else None,
        )
        return self._cached_prefill_metadata

    @property
    def decode_metadata(self) -> Optional["TuringAttentionMetadata"]:
        if self.num_decode_tokens == 0:
            return None

        if self._cached_decode_metadata is not None:
            return self._cached_decode_metadata

        self._cached_decode_metadata = TuringAttentionMetadata(
            num_prefills=0,
            num_prefill_tokens=0,
            num_decode_tokens=self.num_decode_tokens,
            slot_mapping=self.slot_mapping[self.num_prefill_tokens:],
            multi_modal_placeholder_index_maps=None,
            enable_kv_scales_calculation=True,
            seq_lens_tensor=self.seq_lens_tensor[self.num_prefills:] if self.seq_lens_tensor is not None else None,
            max_decode_seq_len=self.max_decode_seq_len,
            block_tables=self.block_tables[self.num_prefills:] if self.block_tables is not None else None,
            seq_lens=None,
            max_query_len=1,  # For decode, query length is always 1
            max_decode_query_len=getattr(self, 'max_decode_query_len', 1),
            max_prefill_seq_len=0,
            use_cuda_graph=self.use_cuda_graph,
            seq_start_loc=None,
            context_lens_tensor=None,
            query_start_loc=None,
        )
        return self._cached_decode_metadata

class TuringAttentionMetadataBuilder(CommonMetadataBuilder[TuringAttentionMetadata]):
    _metadata_cls = TuringAttentionMetadata

    def build(self, seq_lens: List[int], query_lens: List[int],
              cuda_graph_pad_size: int, batch_size: int) -> TuringAttentionMetadata:
        """Build attention metadata with on-device tensors."""
        # Call parent build method to get base metadata
        base_metadata = super().build(seq_lens, query_lens, cuda_graph_pad_size, batch_size)

        # Extract additional fields from base metadata with proper defaults
        # These fields may not exist in all base metadata types, so use getattr with defaults
        block_tables = getattr(base_metadata, 'block_tables', None)
        seq_lens_list = getattr(base_metadata, 'seq_lens', seq_lens)  # Use input seq_lens as fallback
        max_query_len = getattr(base_metadata, 'max_query_len', max(query_lens) if query_lens else 0)
        max_prefill_seq_len = getattr(base_metadata, 'max_prefill_seq_len', max(seq_lens) if seq_lens else 0)
        use_cuda_graph = getattr(base_metadata, 'use_cuda_graph', False)
        seq_start_loc = getattr(base_metadata, 'seq_start_loc', None)
        context_lens_tensor = getattr(base_metadata, 'context_lens_tensor', None)
        query_start_loc = getattr(base_metadata, 'query_start_loc', None)

        # Create TuringAttentionMetadata with additional fields
        return TuringAttentionMetadata(
            num_prefills=base_metadata.num_prefills,
            num_prefill_tokens=base_metadata.num_prefill_tokens,
            num_decode_tokens=base_metadata.num_decode_tokens,
            slot_mapping=base_metadata.slot_mapping,
            multi_modal_placeholder_index_maps=base_metadata.multi_modal_placeholder_index_maps,
            enable_kv_scales_calculation=base_metadata.enable_kv_scales_calculation,
            seq_lens_tensor=base_metadata.seq_lens_tensor,
            max_decode_seq_len=base_metadata.max_decode_seq_len,
            block_tables=block_tables,
            seq_lens=seq_lens_list,
            max_query_len=max_query_len,
            max_prefill_seq_len=max_prefill_seq_len,
            use_cuda_graph=use_cuda_graph,
            seq_start_loc=seq_start_loc,
            context_lens_tensor=context_lens_tensor,
            query_start_loc=query_start_loc,
        )


class TuringAttentionImpl(AttentionImpl[TuringAttentionMetadata]):

    def __init__(
        self,
        num_heads: int,
        head_size: int,
        scale: float,
        num_kv_heads: Optional[int] = None,
        alibi_slopes: Optional[List[float]] = None,
        sliding_window: Optional[int] = None,
        kv_cache_dtype: str = "auto",
        logits_soft_cap: Optional[float] = None,
        attn_type: str = AttentionType.DECODER,
        kv_sharing_target_layer_name: Optional[str] = None,
    ):
        if kv_sharing_target_layer_name is not None:
            raise NotImplementedError("KV sharing is not supported in Turing attention backend.")
        if logits_soft_cap is not None:
            logger.warning_once("TuringAttentionBackend does not support logits_soft_cap. "
                               "Outputs may be slightly different.")

        self.num_heads = num_heads
        self.head_size = head_size
        self.scale = float(scale)
        self.num_kv_heads = num_kv_heads if num_kv_heads is not None else num_heads
        if alibi_slopes is not None:
            raise NotImplementedError("ALiBi slopes are not supported by the Turing attention backend.")
        self.sliding_window = sliding_window
        self.kv_cache_dtype = kv_cache_dtype
        self.logits_soft_cap = logits_soft_cap
        self.attn_type = attn_type
        self.kv_sharing_target_layer_name = kv_sharing_target_layer_name

        # Performance optimization: pre-compute GQA/MQA ratio
        self.num_queries_per_kv = self.num_heads // self.num_kv_heads

        # Validate head size compatibility
        supported_head_sizes = TuringAttentionBackend.get_supported_head_sizes()
        if head_size not in supported_head_sizes:
            raise ValueError(f"Head size {head_size} is not supported by TuringAttentionBackend. "
                           f"Supported head sizes are: {supported_head_sizes}.")

        # Performance optimization: cache frequently used values
        self._head_size_sqrt = head_size ** 0.5

    def forward(
        self,
        layer: AttentionLayer,
        query: torch.Tensor,
        key: torch.Tensor,
        value: torch.Tensor,
        kv_cache: torch.Tensor,
        attn_metadata: TuringAttentionMetadata,
        output: Optional[torch.Tensor] = None,
        output_scale: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        """Forward pass with Turing-optimized attention and PagedAttention.

        Args:
            query: shape = [num_tokens, num_heads * head_size]
            key: shape = [num_tokens, num_kv_heads * head_size]
            value: shape = [num_tokens, num_kv_heads * head_size]
            kv_cache = [2, num_blocks, block_size * num_kv_heads * head_size]
            attn_metadata: Metadata for attention.
            output: Optional output tensor to write results to
            output_scale: Optional scale tensor for quantized output
        Returns:
            shape = [num_tokens, num_heads * head_size]
        """
        assert output is not None, "Output tensor must be provided."

        # Handle output_scale parameter (currently not supported but should not cause errors)
        if output_scale is not None:
            logger.warning_once("TuringAttentionBackend does not support output_scale quantization. "
                               "Output will not be quantized.")

        num_prefill_tokens = attn_metadata.num_prefill_tokens

        # Get k_scale and v_scale from layer if available, otherwise use default
        # Use existing tensors to avoid creating new ones during CUDA graph capture
        if hasattr(layer, '_k_scale') and layer._k_scale is not None:
            k_scale = layer._k_scale
        else:
            k_scale = torch.ones(1, dtype=query.dtype, device=query.device)

        if hasattr(layer, '_v_scale') and layer._v_scale is not None:
            v_scale = layer._v_scale
        else:
            v_scale = torch.ones(1, dtype=query.dtype, device=query.device)

        # Input validation
        if query.dim() != 2:
            raise ValueError(f"Query tensor must be 2D, got {query.dim()}D")
        if query.size(-1) != self.num_heads * self.head_size:
            raise ValueError(f"Query last dimension must be {self.num_heads * self.head_size}, "
                           f"got {query.size(-1)}")

        # Reshape input tensors to proper format for attention computation
        query = query.view(-1, self.num_heads, self.head_size)
        if key is not None:
            assert value is not None, "Key and value must both be provided or both be None"
            if key.dim() != 2:
                raise ValueError(f"Key tensor must be 2D, got {key.dim()}D")
            if value.dim() != 2:
                raise ValueError(f"Value tensor must be 2D, got {value.dim()}D")
            if key.size(-1) != self.num_kv_heads * self.head_size:
                raise ValueError(f"Key last dimension must be {self.num_kv_heads * self.head_size}, "
                               f"got {key.size(-1)}")
            if value.size(-1) != self.num_kv_heads * self.head_size:
                raise ValueError(f"Value last dimension must be {self.num_kv_heads * self.head_size}, "
                               f"got {value.size(-1)}")

            key = key.view(-1, self.num_kv_heads, self.head_size)
            value = value.view(-1, self.num_kv_heads, self.head_size)
        else:
            assert value is None, "Key and value must both be provided or both be None"

        # Validate output tensor
        expected_output_shape = (query.size(0), self.num_heads * self.head_size)
        if output.shape != expected_output_shape:
            raise ValueError(f"Output tensor shape {output.shape} does not match expected {expected_output_shape}")

        # Only update KV cache for decode operations when kv_cache is available
        if kv_cache.numel() > 0 and key is not None and value is not None:
            try:
                key_cache, value_cache = PagedAttention.split_kv_cache(
                    kv_cache, self.num_kv_heads, self.head_size)

                # Write keys and values to cache
                PagedAttention.write_to_paged_cache(
                    key,
                    value,
                    key_cache,
                    value_cache,
                    attn_metadata.slot_mapping,
                    self.kv_cache_dtype,
                    k_scale,
                    v_scale,
                )
            except Exception as e:
                logger.error(f"Failed to update KV cache: {e}")
                raise RuntimeError(f"KV cache update failed: {e}") from e

        # Prefill phase
        if prefill_meta := attn_metadata.prefill_metadata:
            try:
                # We only handle the case where there is no prefix caching.
                # If there is prefix caching, PagedAttention.forward_prefix should be used,
                # but that is a more complex kernel. This implementation focuses on the
                # common case of a new prompt.
                if (kv_cache.numel() == 0 or
                    prefill_meta.block_tables is None or
                    prefill_meta.block_tables.numel() == 0):
                    # Use Turing-optimized kernel for prefill
                    prefill_query = query[:num_prefill_tokens]
                    prefill_key = key[:num_prefill_tokens] if key is not None else None
                    prefill_value = value[:num_prefill_tokens] if value is not None else None

                    # Validate prefill inputs
                    if prefill_key is None or prefill_value is None:
                        raise ValueError("Prefill phase requires both key and value tensors")

                    out = _run_turing_flash_attention_forward(
                        prefill_query,
                        prefill_key,
                        prefill_value,
                        prefill_meta,
                        self.num_heads,
                        self.scale
                    )
                    # The Turing kernel returns [num_tokens, num_heads * head_size], reshape to [num_tokens, num_heads, head_size]
                    if out.shape != (num_prefill_tokens, self.num_heads * self.head_size):
                        raise ValueError(f"Turing kernel output shape {out.shape} does not match expected "
                                       f"{(num_prefill_tokens, self.num_heads * self.head_size)}")
                    output[:num_prefill_tokens] = out.view(num_prefill_tokens, self.num_heads, self.head_size)
                else:
                    # Fallback for prefix caching
                    logger.warning_once("TuringAttentionBackend using PagedAttention for prefix caching.")

                    prefill_query = query[:num_prefill_tokens]
                    prefill_key = key[:num_prefill_tokens] if key is not None else None
                    prefill_value = value[:num_prefill_tokens] if value is not None else None

                    # Validate required fields for prefix caching
                    if prefill_meta.query_start_loc is None:
                        raise ValueError("query_start_loc is required for prefix caching")
                    if prefill_meta.seq_lens_tensor is None:
                        raise ValueError("seq_lens_tensor is required for prefix caching")

                    out = PagedAttention.forward_prefix(
                        prefill_query,
                        prefill_key,
                        prefill_value,
                        self.kv_cache_dtype,
                        key_cache,
                        value_cache,
                        prefill_meta.block_tables,
                        prefill_meta.query_start_loc,
                        prefill_meta.seq_lens_tensor,
                        prefill_meta.max_prefill_seq_len,
                        None,  # alibi_slopes
                        self.sliding_window,
                        k_scale,
                        v_scale,
                    )
                    # PagedAttention returns [num_tokens, num_heads, head_size], which is what output buffer expects
                    output[:num_prefill_tokens] = out
            except Exception as e:
                logger.error(f"Prefill phase failed: {e}")
                raise RuntimeError(f"Prefill computation failed: {e}") from e

        # Decode phase - always use PagedAttention for decode as it's optimized for single-token generation
        if decode_meta := attn_metadata.decode_metadata:
            try:
                decode_query = query[num_prefill_tokens:]

                # Validate decode inputs
                if decode_meta.block_tables is None:
                    raise ValueError("block_tables is required for decode phase")
                if decode_meta.seq_lens_tensor is None:
                    raise ValueError("seq_lens_tensor is required for decode phase")
                if decode_meta.max_decode_seq_len is None:
                    raise ValueError("max_decode_seq_len is required for decode phase")

                decode_output = PagedAttention.forward_decode(
                    decode_query,
                    key_cache,
                    value_cache,
                    decode_meta.block_tables,
                    decode_meta.seq_lens_tensor,
                    decode_meta.max_decode_seq_len,
                    self.kv_cache_dtype,
                    self.num_kv_heads,
                    self.scale,
                    alibi_slopes=None,
                    k_scale=k_scale,
                    v_scale=v_scale,
                )
                # PagedAttention.forward_decode returns [num_tokens, num_heads, head_size], which is what output buffer expects
                expected_decode_shape = (attn_metadata.num_decode_tokens, self.num_heads, self.head_size)
                if decode_output.shape != expected_decode_shape:
                    raise ValueError(f"Decode output shape {decode_output.shape} does not match expected {expected_decode_shape}")
                output[num_prefill_tokens:] = decode_output
            except Exception as e:
                logger.error(f"Decode phase failed: {e}")
                raise RuntimeError(f"Decode computation failed: {e}") from e

        # Reshape the output tensor back to the expected format
        return output.view(-1, self.num_heads * self.head_size)


def _run_turing_flash_attention_forward(
    query: torch.Tensor,
    key: torch.Tensor,
    value: torch.Tensor,
    attn_metadata: TuringAttentionMetadata,
    num_heads: int,
    scale: float,
) -> torch.Tensor:
    """
    Optimized wrapper to call the Triton kernel for the prefill phase with chunked prefill support.

    Args:
        query: [num_tokens, num_heads, head_size]
        key: [num_tokens, num_kv_heads, head_size]
        value: [num_tokens, num_kv_heads, head_size]
    """
    # Validate inputs
    if query is None or key is None or value is None:
        raise ValueError("Query, key, and value tensors must not be None")

    # Use seq_start_loc for chunked prefill support, fallback to seq_lens for compatibility
    if attn_metadata.seq_start_loc is not None:
        # Chunked prefill mode: use seq_start_loc tensor
        seq_start_loc = attn_metadata.seq_start_loc
        if seq_start_loc.numel() < 2:
            raise ValueError("seq_start_loc must have at least 2 elements for chunked prefill")

        # Keep seq_start_loc on GPU to avoid CPU-GPU transfers
        seq_lens_tensor = seq_start_loc[1:] - seq_start_loc[:-1]
    elif attn_metadata.seq_lens is not None and len(attn_metadata.seq_lens) > 0:
        # Legacy mode: use seq_lens list - convert to tensor for efficiency
        seq_lens_tensor = torch.tensor(attn_metadata.seq_lens, dtype=torch.int32, device=query.device)
    else:
        raise ValueError("Either seq_start_loc or seq_lens is required for Turing attention")

    # Input tensors are already in [num_tokens, num_heads, head_size] format
    num_tokens, _, head_size = query.shape
    _, num_kv_heads, _ = key.shape

    # Handle GQA/MQA by repeating KV heads if needed - optimized memory-efficient operation
    if num_kv_heads != num_heads:
        num_queries_per_kv = num_heads // num_kv_heads
        # Use helper function for efficient KV head repetition
        key = _repeat_kv_heads(key, num_queries_per_kv)
        value = _repeat_kv_heads(value, num_queries_per_kv)

    if head_size not in [16, 32, 64, 128]:
        raise ValueError(f"Head size {head_size} not supported by Turing backend. Supported sizes: [16, 32, 64, 128]")

    # Validate tensor shapes after potential KV head expansion
    if key.shape != (num_tokens, num_heads, head_size):
        raise ValueError(f"Key tensor shape {key.shape} does not match expected {(num_tokens, num_heads, head_size)}")
    if value.shape != (num_tokens, num_heads, head_size):
        raise ValueError(f"Value tensor shape {value.shape} does not match expected {(num_tokens, num_heads, head_size)}")

    # Check if Triton is available, otherwise use fallback
    if not TRITON_AVAILABLE:
        logger.warning_once("Triton not available, using PyTorch SDPA fallback for Turing attention")
        return _turing_sdpa_fallback(query, key, value, attn_metadata, scale)

    # Use optimized varlen attention kernel with GPU tensors
    if attn_metadata.seq_start_loc is not None:
        # Use GPU-based processing for chunked prefill
        output = _turing_varlen_attention_kernel_optimized(
            query, key, value, attn_metadata.seq_start_loc, scale, is_causal=True
        )
    else:
        # Fallback to CPU-based processing for legacy mode
        seq_lens = seq_lens_tensor.cpu().tolist()
        output = _turing_varlen_attention_kernel(
            query, key, value, seq_lens, scale, is_causal=True
        )

    return output  # [num_tokens, num_heads * head_size]


def _turing_sdpa_fallback(
    query: torch.Tensor,
    key: torch.Tensor,
    value: torch.Tensor,
    attn_metadata: TuringAttentionMetadata,
    scale: float,
) -> torch.Tensor:
    """
    Fallback implementation using PyTorch SDPA when Triton is not available.

    Args:
        query: [num_tokens, num_heads, head_size]
        key: [num_tokens, num_heads, head_size]
        value: [num_tokens, num_heads, head_size]
        attn_metadata: Attention metadata
        scale: Attention scale factor

    Returns:
        output: [num_tokens, num_heads * head_size]
    """
    num_tokens, num_heads, head_size = query.shape

    # Get sequence lengths
    if attn_metadata.seq_start_loc is not None:
        seq_lens = (attn_metadata.seq_start_loc[1:] - attn_metadata.seq_start_loc[:-1]).cpu().tolist()
    elif attn_metadata.seq_lens is not None:
        seq_lens = attn_metadata.seq_lens
    else:
        # Single sequence case
        seq_lens = [num_tokens]

    # Process each sequence separately using SDPA
    output = torch.empty(num_tokens, num_heads * head_size, dtype=query.dtype, device=query.device)
    start_idx = 0

    for seq_len in seq_lens:
        if seq_len == 0:
            continue

        end_idx = start_idx + seq_len

        # Extract sequence tensors
        q_seq = query[start_idx:end_idx].transpose(0, 1)  # [num_heads, seq_len, head_size]
        k_seq = key[start_idx:end_idx].transpose(0, 1)    # [num_heads, seq_len, head_size]
        v_seq = value[start_idx:end_idx].transpose(0, 1)  # [num_heads, seq_len, head_size]

        # Apply SDPA with causal masking
        with torch.nn.attention.sdpa_kernel(torch.nn.attention.SDPBackend.MATH):
            out_seq = torch.nn.functional.scaled_dot_product_attention(
                q_seq, k_seq, v_seq,
                is_causal=True,
                scale=scale
            )

        # Reshape and store output
        out_seq = out_seq.transpose(0, 1)  # [seq_len, num_heads, head_size]
        output[start_idx:end_idx] = out_seq.contiguous().view(seq_len, num_heads * head_size)

        start_idx = end_idx

    return output


def _repeat_kv_heads(tensor: torch.Tensor, num_queries_per_kv: int) -> torch.Tensor:
    """
    Efficiently repeat KV heads for GQA/MQA.

    Args:
        tensor: [num_tokens, num_kv_heads, head_size]
        num_queries_per_kv: Number of query heads per KV head

    Returns:
        tensor: [num_tokens, num_heads, head_size] where num_heads = num_kv_heads * num_queries_per_kv
    """
    if num_queries_per_kv == 1:
        return tensor

    num_tokens, num_kv_heads, head_size = tensor.shape
    # Use expand for memory efficiency - creates a view without copying data
    return tensor.unsqueeze(2).expand(
        num_tokens, num_kv_heads, num_queries_per_kv, head_size
    ).contiguous().view(num_tokens, num_kv_heads * num_queries_per_kv, head_size)


# Triton kernel definitions (only available when Triton is imported)
if TRITON_AVAILABLE:
    @triton.jit
    def _turing_attention_kernel_forward(
        Q, K, V, Out,
        q_stride_z, q_stride_h, q_stride_m, q_stride_k,
        k_stride_z, k_stride_h, k_stride_n, k_stride_k,
        v_stride_z, v_stride_h, v_stride_n, v_stride_k,
        out_stride_z, out_stride_h, out_stride_m, out_stride_k,
        Z, H, N_CTX,
        SCALE: tl.constexpr,
        D_HEAD: tl.constexpr,
        BLOCK_M: tl.constexpr,
        BLOCK_N: tl.constexpr,
        IS_CAUSAL: tl.constexpr,
    ):
        start_m = tl.program_id(0)
        off_z = tl.program_id(1)
        off_h = tl.program_id(2)

        # Early exit for out-of-bounds threads
        if ((off_z >= Z) or (off_h >= H)) or (start_m * BLOCK_M >= N_CTX):
            return

        # Pre-compute offsets for better memory access
        q_offset = off_z * q_stride_z + off_h * q_stride_h
        k_offset = off_z * k_stride_z + off_h * k_stride_h
        v_offset = off_z * v_stride_z + off_h * v_stride_h
        out_offset = off_z * out_stride_z + off_h * out_stride_h

        # Create block pointers with optimized memory layout
        Q_block_ptr = tl.make_block_ptr(
            base=Q + q_offset,
            shape=(N_CTX, D_HEAD),
            strides=(q_stride_m, q_stride_k),
            offsets=(start_m * BLOCK_M, 0),
            block_shape=(BLOCK_M, D_HEAD),
            order=(1, 0)
        )
        K_block_ptr = tl.make_block_ptr(
            base=K + k_offset,
            shape=(D_HEAD, N_CTX),
            strides=(k_stride_k, k_stride_n),
            offsets=(0, 0),
            block_shape=(D_HEAD, BLOCK_N),
            order=(0, 1)
        )
        V_block_ptr = tl.make_block_ptr(
            base=V + v_offset,
            shape=(N_CTX, D_HEAD),
            strides=(v_stride_n, v_stride_k),
            offsets=(0, 0),
            block_shape=(BLOCK_N, D_HEAD),
            order=(1, 0)
        )
        Out_block_ptr = tl.make_block_ptr(
            base=Out + out_offset,
            shape=(N_CTX, D_HEAD),
            strides=(out_stride_m, out_stride_k),
            offsets=(start_m * BLOCK_M, 0),
            block_shape=(BLOCK_M, D_HEAD),
            order=(1, 0)
        )

        # Initialize accumulators with proper data types
        acc = tl.zeros([BLOCK_M, D_HEAD], dtype=tl.float32)
        m_i = tl.full([BLOCK_M, 1], value=float('-inf'), dtype=tl.float32)
        l_i = tl.zeros([BLOCK_M, 1], dtype=tl.float32)

        # Load and scale query once
        q = tl.load(Q_block_ptr, boundary_check=(0, 1))
        q = (q * SCALE).to(tl.float32)

        # Optimize loop bounds for causal attention
        if IS_CAUSAL:
            loop_end = tl.minimum(N_CTX, (start_m + 1) * BLOCK_M)
        else:
            loop_end = N_CTX

        # Pre-compute causal mask offsets if needed
        if IS_CAUSAL:
            offs_m = start_m * BLOCK_M + tl.arange(0, BLOCK_M)
            offs_m = tl.where(offs_m < N_CTX, offs_m, N_CTX - 1)

        # Main attention loop with optimized memory access
        for start_n in range(0, loop_end, BLOCK_N):
            # Load K and V blocks
            k = tl.load(K_block_ptr, boundary_check=(0, 1))
            v = tl.load(V_block_ptr, boundary_check=(0, 1))

            # Compute attention scores
            s_ij = tl.dot(q, k, out_dtype=tl.float32)

            # Apply causal mask if needed
            if IS_CAUSAL:
                offs_n = start_n + tl.arange(0, BLOCK_N)
                offs_n = tl.where(offs_n < N_CTX, offs_n, N_CTX - 1)
                causal_mask = offs_m[:, None] >= offs_n[None, :]
                s_ij = tl.where(causal_mask, s_ij, float('-inf'))

            # Online softmax computation
            m_ij = tl.max(s_ij, 1)[:, None]
            m_i_new = tl.maximum(m_i, m_ij)
            exp_diff = tl.exp(m_i - m_i_new)

            # Update accumulator and normalizer
            acc = acc * exp_diff
            l_i = l_i * exp_diff

            p_ij = tl.exp(s_ij - m_i_new)
            l_i += tl.sum(p_ij, 1)[:, None]

            # Accumulate weighted values
            p_ij = p_ij.to(v.dtype)
            acc += tl.dot(p_ij, v)
            m_i = m_i_new

            # Advance block pointers
            K_block_ptr = tl.advance(K_block_ptr, (0, BLOCK_N))
            V_block_ptr = tl.advance(V_block_ptr, (BLOCK_N, 0))

        # Final normalization and output
        l_i_safe = tl.where(l_i == 0, 1.0, l_i)
        out = (acc / l_i_safe).to(Out.dtype.element_ty)
        tl.store(Out_block_ptr, out, boundary_check=(0, 1))

else:
    # Fallback implementations when Triton is not available
    def _turing_attention_kernel_forward(*args, **kwargs):
        raise RuntimeError("Triton not available, cannot use Turing attention kernel")


def _turing_attention_kernel(q: torch.Tensor, k: torch.Tensor, v: torch.Tensor, is_causal: bool, scale: float) -> torch.Tensor:
    """
    Turing-optimized attention kernel with fallback support.

    Args:
        q: [batch_size, num_heads, seq_len, head_size]
        k: [batch_size, num_heads, seq_len, head_size]
        v: [batch_size, num_heads, seq_len, head_size]
        is_causal: Whether to apply causal masking
        scale: Attention scale factor

    Returns:
        output: [batch_size, num_heads, seq_len, head_size]
    """
    if not TRITON_AVAILABLE:
        # Fallback to PyTorch SDPA
        return torch.nn.functional.scaled_dot_product_attention(
            q, k, v, is_causal=is_causal, scale=scale
        )

    shape = q.shape
    Z, H, N_CTX, D_HEAD = shape

    o = torch.empty_like(q)

    # Optimized block sizes for Turing architecture based on sequence length
    # Use larger blocks for longer sequences to improve efficiency
    if N_CTX <= 512:
        BLOCK_M = 64
        BLOCK_N = 64
    elif N_CTX <= 2048:
        BLOCK_M = 128
        BLOCK_N = 64
    else:
        BLOCK_M = 128
        BLOCK_N = 32

    # Ensure block sizes don't exceed sequence length
    BLOCK_M = min(BLOCK_M, N_CTX)
    BLOCK_N = min(BLOCK_N, N_CTX)

    grid = (triton.cdiv(N_CTX, BLOCK_M), Z, H)

    _turing_attention_kernel_forward[grid](
        q, k, v, o,
        q.stride(0), q.stride(1), q.stride(2), q.stride(3),
        k.stride(0), k.stride(1), k.stride(2), k.stride(3),
        v.stride(0), v.stride(1), v.stride(2), v.stride(3),
        o.stride(0), o.stride(1), o.stride(2), o.stride(3),
        Z, H, N_CTX,
        SCALE=scale,
        D_HEAD=D_HEAD,
        BLOCK_M=BLOCK_M,
        BLOCK_N=BLOCK_N,
        IS_CAUSAL=is_causal,
    )
    return o


def _turing_varlen_attention_kernel_optimized(
    query: torch.Tensor,
    key: torch.Tensor,
    value: torch.Tensor,
    seq_start_loc: torch.Tensor,
    scale: float,
    is_causal: bool = True,
) -> torch.Tensor:
    """
    Optimized variable-length attention kernel using GPU-based sequence processing.

    This version avoids CPU-GPU transfers and processes sequences more efficiently
    by using vectorized operations where possible.

    Args:
        query: [num_tokens, num_heads, head_size]
        key: [num_tokens, num_heads, head_size]
        value: [num_tokens, num_heads, head_size]
        seq_start_loc: [batch_size + 1] tensor with sequence start locations
        scale: Attention scale factor
        is_causal: Whether to apply causal masking

    Returns:
        output: [num_tokens, num_heads * head_size]
    """
    num_tokens, num_heads, head_size = query.shape
    batch_size = seq_start_loc.numel() - 1

    # Validate input shapes
    if query.shape != (num_tokens, num_heads, head_size):
        raise ValueError(f"Query shape {query.shape} does not match expected {(num_tokens, num_heads, head_size)}")
    if key.shape != (num_tokens, num_heads, head_size):
        raise ValueError(f"Key shape {key.shape} does not match expected {(num_tokens, num_heads, head_size)}")
    if value.shape != (num_tokens, num_heads, head_size):
        raise ValueError(f"Value shape {value.shape} does not match expected {(num_tokens, num_heads, head_size)}")

    # For small batch sizes, use the optimized single-sequence processing
    if batch_size <= 4:
        # Convert to CPU only once for small batches
        seq_lens = (seq_start_loc[1:] - seq_start_loc[:-1]).cpu().tolist()
        return _turing_varlen_attention_kernel(query, key, value, seq_lens, scale, is_causal)

    # For larger batches, try to process multiple sequences together when possible
    # Group sequences by similar lengths to enable batched processing
    seq_lens_tensor = seq_start_loc[1:] - seq_start_loc[:-1]
    max_seq_len = seq_lens_tensor.max().item()

    # If all sequences are similar length (within 25% of max), use batched processing
    min_seq_len = seq_lens_tensor.min().item()
    if max_seq_len > 0 and min_seq_len / max_seq_len > 0.75:
        return _turing_batched_attention_kernel(
            query, key, value, seq_start_loc, scale, is_causal
        )

    # Otherwise, fall back to sequential processing but with optimizations
    seq_lens = seq_lens_tensor.cpu().tolist()
    return _turing_varlen_attention_kernel(query, key, value, seq_lens, scale, is_causal)


def _turing_batched_attention_kernel(
    query: torch.Tensor,
    key: torch.Tensor,
    value: torch.Tensor,
    seq_start_loc: torch.Tensor,
    scale: float,
    is_causal: bool = True,
) -> torch.Tensor:
    """
    Batched attention kernel for sequences of similar lengths.

    This kernel processes multiple sequences together by padding them to the same length,
    which is more efficient when sequences have similar lengths.
    """
    num_tokens, num_heads, head_size = query.shape
    batch_size = seq_start_loc.numel() - 1
    seq_lens_tensor = seq_start_loc[1:] - seq_start_loc[:-1]
    max_seq_len = seq_lens_tensor.max().item()

    # Create padded tensors for batched processing
    q_batched = torch.zeros(batch_size, max_seq_len, num_heads, head_size,
                           dtype=query.dtype, device=query.device)
    k_batched = torch.zeros(batch_size, max_seq_len, num_heads, head_size,
                           dtype=key.dtype, device=key.device)
    v_batched = torch.zeros(batch_size, max_seq_len, num_heads, head_size,
                           dtype=value.dtype, device=value.device)

    # Fill batched tensors
    for i in range(batch_size):
        start_idx = seq_start_loc[i].item()
        end_idx = seq_start_loc[i + 1].item()
        seq_len = end_idx - start_idx

        q_batched[i, :seq_len] = query[start_idx:end_idx]
        k_batched[i, :seq_len] = key[start_idx:end_idx]
        v_batched[i, :seq_len] = value[start_idx:end_idx]

    # Reshape for attention kernel: [batch_size, num_heads, max_seq_len, head_size]
    q_batched = q_batched.transpose(1, 2)
    k_batched = k_batched.transpose(1, 2)
    v_batched = v_batched.transpose(1, 2)

    # Run batched attention
    o_batched = _turing_attention_kernel(q_batched, k_batched, v_batched, is_causal, scale)

    # Extract results back to original format
    output = torch.empty(num_tokens, num_heads * head_size,
                        dtype=query.dtype, device=query.device)

    o_batched = o_batched.transpose(1, 2)  # [batch_size, max_seq_len, num_heads, head_size]

    for i in range(batch_size):
        start_idx = seq_start_loc[i].item()
        end_idx = seq_start_loc[i + 1].item()
        seq_len = end_idx - start_idx

        output[start_idx:end_idx] = o_batched[i, :seq_len].reshape(seq_len, num_heads * head_size)

    return output


def _turing_varlen_attention_kernel(
    query: torch.Tensor,
    key: torch.Tensor,
    value: torch.Tensor,
    seq_lens: List[int],
    scale: float,
    is_causal: bool = True,
) -> torch.Tensor:
    """
    Variable-length attention kernel for chunked prefill support.
    Optimized version with reduced memory allocations and tensor operations.

    Args:
        query: [num_tokens, num_heads, head_size]
        key: [num_tokens, num_heads, head_size]
        value: [num_tokens, num_heads, head_size]
        seq_lens: List of sequence lengths
        scale: Attention scale factor
        is_causal: Whether to apply causal masking

    Returns:
        output: [num_tokens, num_heads * head_size]
    """
    num_tokens, num_heads, head_size = query.shape

    # Pre-allocate output tensor with proper memory layout
    output = torch.empty(num_tokens, num_heads * head_size,
                        dtype=query.dtype, device=query.device,
                        memory_format=torch.contiguous_format)

    # Performance optimization: batch small sequences together
    small_sequences = []
    large_sequences = []
    start_idx = 0

    for seq_len in seq_lens:
        if seq_len == 0:
            continue
        end_idx = start_idx + seq_len

        if seq_len <= 128:  # Threshold for small sequences
            small_sequences.append((start_idx, end_idx, seq_len))
        else:
            large_sequences.append((start_idx, end_idx, seq_len))

        start_idx = end_idx

    # Process large sequences individually for better memory efficiency
    for start_idx, end_idx, seq_len in large_sequences:
        # Extract sequence tensors - use slicing for efficiency
        q_seq = query[start_idx:end_idx]  # [seq_len, num_heads, head_size]
        k_seq = key[start_idx:end_idx]    # [seq_len, num_heads, head_size]
        v_seq = value[start_idx:end_idx]  # [seq_len, num_heads, head_size]

        # Reshape for batch processing: [1, num_heads, seq_len, head_size]
        # Use transpose for better memory access patterns
        q_batch = q_seq.transpose(0, 1).unsqueeze(0)  # [1, num_heads, seq_len, head_size]
        k_batch = k_seq.transpose(0, 1).unsqueeze(0)  # [1, num_heads, seq_len, head_size]
        v_batch = v_seq.transpose(0, 1).unsqueeze(0)  # [1, num_heads, seq_len, head_size]

        # Run attention kernel for this sequence
        o_batch = _turing_attention_kernel(q_batch, k_batch, v_batch, is_causal, scale)

        # Reshape back and store directly in output buffer
        o_seq = o_batch.squeeze(0).transpose(0, 1)  # [seq_len, num_heads, head_size]
        output[start_idx:end_idx] = o_seq.contiguous().view(seq_len, num_heads * head_size)

    # Process small sequences (could be batched together in future optimization)
    for start_idx, end_idx, seq_len in small_sequences:
        # For now, process individually but with optimized memory access
        q_seq = query[start_idx:end_idx]
        k_seq = key[start_idx:end_idx]
        v_seq = value[start_idx:end_idx]

        q_batch = q_seq.transpose(0, 1).unsqueeze(0)
        k_batch = k_seq.transpose(0, 1).unsqueeze(0)
        v_batch = v_seq.transpose(0, 1).unsqueeze(0)

        o_batch = _turing_attention_kernel(q_batch, k_batch, v_batch, is_causal, scale)
        o_seq = o_batch.squeeze(0).transpose(0, 1)
        output[start_idx:end_idx] = o_seq.contiguous().view(seq_len, num_heads * head_size)

    return output
